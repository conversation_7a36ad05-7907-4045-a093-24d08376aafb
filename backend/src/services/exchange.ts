import mongoose from 'mongoose';
import ExchangeOffer from '../models/ExchangeOffer';
import Transaction from '../models/Transaction';
import ClothingItem from '../models/ClothingItem';
import User from '../models/User';
import ExchangePreferences from '../models/ExchangePreferences';
import CharityPartner from '../models/CharityPartner';
import { tokenService } from './token';

export interface CreateOfferData {
  type: 'swap' | 'token_purchase' | 'donation';
  requestedItems: string[];
  offeredItems?: string[];
  tokenAmount?: number;
  message?: string;
  conditions?: string;
  preferredDeliveryMethod: 'pickup' | 'delivery' | 'meetup';
  deliveryNotes?: string;
  charityPartnerId?: string;
}

export interface CounterOfferData {
  tokenAmount?: number;
  offeredItems?: string[];
  message?: string;
}

class ExchangeService {
  // Create a new exchange offer
  async createOffer(offererId: string, targetUserId: string, offerData: CreateOfferData) {
    console.log('=== EXCHANGE SERVICE CREATE OFFER ===');
    console.log('Offerer ID:', offererId);
    console.log('Target User ID:', targetUserId);
    console.log('Offer Data:', JSON.stringify(offerData, null, 2));

    try {
      // Validate users exist
      const [offerer, targetUser] = await Promise.all([
        User.findById(offererId),
        User.findById(targetUserId)
      ]);

      if (!offerer || !targetUser) {
        throw new Error('User not found');
      }

      // Validate requested items exist and belong to target user
      const requestedItems = await ClothingItem.find({
        _id: { $in: offerData.requestedItems },
        owner: targetUserId,
        isAvailable: true
      });

      if (requestedItems.length !== offerData.requestedItems.length) {
        throw new Error('Some requested items are not available or do not belong to the target user');
      }

      // Validate offered items (for swaps) belong to offerer
      if (offerData.offeredItems && offerData.offeredItems.length > 0) {
        const offeredItems = await ClothingItem.find({
          _id: { $in: offerData.offeredItems },
          owner: offererId,
          isAvailable: true
        });

        if (offeredItems.length !== offerData.offeredItems.length) {
          throw new Error('Some offered items are not available or do not belong to you');
        }
      }

      // Check user preferences
      const targetPreferences = await ExchangePreferences.findOne({ user: targetUserId });
      if (targetPreferences) {
        // Check if target user accepts this exchange type
        if (!targetPreferences.acceptsExchangeType(offerData.type)) {
          throw new Error('Target user does not accept this type of exchange');
        }

        // Check minimum token amount for purchases
        if (offerData.type === 'token_purchase' && offerData.tokenAmount && 
            offerData.tokenAmount < targetPreferences.minimumTokenOffer) {
          throw new Error(`Minimum token offer is ${targetPreferences.minimumTokenOffer}`);
        }

        // Check if offerer is blocked
        if (targetPreferences.isUserBlocked(new mongoose.Types.ObjectId(offererId))) {
          throw new Error('You are blocked by this user');
        }
      }

      // For token purchases, verify offerer has sufficient balance
      if (offerData.type === 'token_purchase' && offerData.tokenAmount) {
        const offererBalance = await tokenService.getBalance(offererId);
        if (offererBalance < offerData.tokenAmount) {
          throw new Error('Insufficient token balance');
        }
      }

      // For donations, validate charity partner
      let charityPartner = null;
      if (offerData.type === 'donation' && offerData.charityPartnerId) {
        charityPartner = await CharityPartner.findById(offerData.charityPartnerId);
        if (!charityPartner || !charityPartner.isActive) {
          throw new Error('Invalid or inactive charity partner');
        }
      }

      // Create the offer
      const offer = new ExchangeOffer({
        type: offerData.type,
        offerer: offererId,
        targetUser: targetUserId,
        requestedItems: offerData.requestedItems,
        offeredItems: offerData.offeredItems,
        tokenAmount: offerData.tokenAmount,
        message: offerData.message,
        conditions: offerData.conditions,
        preferredDeliveryMethod: offerData.preferredDeliveryMethod,
        deliveryNotes: offerData.deliveryNotes,
        charityPartner: charityPartner ? {
          name: charityPartner.name,
          id: charityPartner._id,
          category: charityPartner.category
        } : undefined
      });

      await offer.save();

      // Send exchange request notification
      try {
        const { notificationIntegrationService } = await import('./notificationIntegration');
        const offererUser = await User.findById(offererId);
        const requestedItemNames = requestedItems.map(item => item.name).join(', ');

        if (offererUser) {
          await notificationIntegrationService.sendExchangeRequest(
            targetUserId,
            offererUser.fullName,
            requestedItemNames,
            offer._id.toString()
          );
        }
      } catch (notificationError) {
        // Don't fail the transaction for notification errors
        console.error('Failed to send exchange request notification:', notificationError);
      }

      // Auto-accept if target user has auto-accept enabled for this type
      if (targetPreferences) {
        const shouldAutoAccept = 
          (offerData.type === 'swap' && targetPreferences.autoAcceptSwaps) ||
          (offerData.type === 'token_purchase' && targetPreferences.autoAcceptTokenPurchases) ||
          (offerData.type === 'donation' && targetPreferences.autoAcceptDonations);

        if (shouldAutoAccept) {
          await this.acceptOffer(offer._id.toString(), targetUserId);
        }
      }

      return await ExchangeOffer.findById(offer._id)
        .populate('offerer', 'name profilePicture location')
        .populate('targetUser', 'name profilePicture location')
        .populate('requestedItems')
        .populate('offeredItems');

    } catch (error) {
      console.log('=== EXCHANGE SERVICE ERROR ===');
      console.log('Error:', error);
      throw error;
    }
  }

  // Accept an exchange offer
  async acceptOffer(offerId: string, userId: string, session?: mongoose.ClientSession) {
    // For now, we'll skip transactions in development
    // const useSession = session || await mongoose.startSession();
    // if (!session) useSession.startTransaction();

    try {
      const offer = await ExchangeOffer.findById(offerId);
      if (!offer) {
        throw new Error('Offer not found');
      }

      if (offer.targetUser.toString() !== userId) {
        throw new Error('You can only accept offers made to you');
      }

      if (offer.status !== 'pending') {
        throw new Error('Offer is no longer pending');
      }

      // Update offer status
      offer.status = 'accepted';
      await offer.save();

      // Create transaction
      const transaction = new Transaction({
        type: offer.type,
        initiator: offer.offerer,
        recipient: offer.targetUser,
        initiatorItems: offer.offeredItems,
        recipientItems: offer.requestedItems,
        tokenAmount: offer.tokenAmount,
        deliveryMethod: offer.preferredDeliveryMethod,
        charityPartner: offer.charityPartner,
        timeline: [{
          status: 'pending',
          timestamp: new Date(),
          updatedBy: offer.targetUser,
          note: 'Offer accepted, transaction created'
        }]
      });

      await transaction.save();

      // Send exchange accepted notification
      try {
        const { notificationIntegrationService } = await import('./notificationIntegration');
        const targetUser = await User.findById(userId);
        const offererUser = await User.findById(offer.offerer);
        const requestedItemNames = offer.requestedItems ?
          (await ClothingItem.find({ _id: { $in: offer.requestedItems } }))
            .map(item => item.name).join(', ') : 'items';

        if (targetUser && offererUser) {
          // Notify the offerer that their request was accepted
          await notificationIntegrationService.sendExchangeStatusUpdate(
            offer.offerer.toString(),
            'accepted',
            requestedItemNames,
            targetUser.fullName,
            offer._id.toString()
          );
        }
      } catch (notificationError) {
        // Don't fail the transaction for notification errors
        console.error('Failed to send exchange accepted notification:', notificationError);
      }

      // Update item availability to false (in transaction)
      const allItems = [...(offer.requestedItems || []), ...(offer.offeredItems || [])];
      await ClothingItem.updateMany(
        { _id: { $in: allItems } },
        { isAvailable: false }
      );

      return transaction;

    } catch (error) {
      throw error;
    }
  }

  // Decline an exchange offer
  async declineOffer(offerId: string, userId: string) {
    const offer = await ExchangeOffer.findById(offerId);
    if (!offer) {
      throw new Error('Offer not found');
    }

    if (offer.targetUser.toString() !== userId) {
      throw new Error('You can only decline offers made to you');
    }

    if (offer.status !== 'pending') {
      throw new Error('Offer is no longer pending');
    }

    offer.status = 'declined';
    await offer.save();

    return offer;
  }

  // Withdraw an exchange offer
  async withdrawOffer(offerId: string, userId: string) {
    const offer = await ExchangeOffer.findById(offerId);
    if (!offer) {
      throw new Error('Offer not found');
    }

    if (offer.offerer.toString() !== userId) {
      throw new Error('You can only withdraw your own offers');
    }

    if (offer.status !== 'pending') {
      throw new Error('Offer is no longer pending');
    }

    offer.status = 'withdrawn';
    await offer.save();

    return offer;
  }

  // Create a counter offer
  async createCounterOffer(offerId: string, userId: string, counterOfferData: CounterOfferData) {
    const offer = await ExchangeOffer.findById(offerId);
    if (!offer) {
      throw new Error('Offer not found');
    }

    if (offer.targetUser.toString() !== userId) {
      throw new Error('You can only counter offers made to you');
    }

    if (offer.status !== 'pending') {
      throw new Error('Offer is no longer pending');
    }

    // Validate offered items belong to user
    if (counterOfferData.offeredItems && counterOfferData.offeredItems.length > 0) {
      const offeredItems = await ClothingItem.find({
        _id: { $in: counterOfferData.offeredItems },
        owner: userId,
        isAvailable: true
      });

      if (offeredItems.length !== counterOfferData.offeredItems.length) {
        throw new Error('Some offered items are not available or do not belong to you');
      }
    }

    // Add counter offer to the offer
    offer.counterOffers.push({
      offeredBy: new mongoose.Types.ObjectId(userId),
      tokenAmount: counterOfferData.tokenAmount,
      offeredItems: counterOfferData.offeredItems?.map(id => new mongoose.Types.ObjectId(id)),
      message: counterOfferData.message,
      createdAt: new Date()
    });

    await offer.save();

    return await ExchangeOffer.findById(offer._id)
      .populate('offerer', 'name profilePicture')
      .populate('targetUser', 'name profilePicture')
      .populate('counterOffers.offeredItems');
  }

  // Get offers for a user (received and sent)
  async getUserOffers(userId: string, type?: 'received' | 'sent', status?: string) {
    const query: any = {};
    
    if (type === 'received') {
      query.targetUser = userId;
    } else if (type === 'sent') {
      query.offerer = userId;
    } else {
      query.$or = [{ targetUser: userId }, { offerer: userId }];
    }

    if (status) {
      query.status = status;
    }

    return await ExchangeOffer.find(query)
      .populate('offerer', 'name profilePicture location')
      .populate('targetUser', 'name profilePicture location')
      .populate('requestedItems')
      .populate('offeredItems')
      .populate('counterOffers.offeredItems')
      .sort({ createdAt: -1 });
  }

  // Get offer details
  async getOfferDetails(offerId: string, userId: string) {
    const offer = await ExchangeOffer.findById(offerId)
      .populate('offerer', 'name profilePicture location rating')
      .populate('targetUser', 'name profilePicture location rating')
      .populate('requestedItems')
      .populate('offeredItems')
      .populate('counterOffers.offeredItems')
      .populate('counterOffers.offeredBy', 'name profilePicture');

    if (!offer) {
      throw new Error('Offer not found');
    }

    // Check if user is involved in this offer
    if (offer.offerer._id.toString() !== userId && offer.targetUser._id.toString() !== userId) {
      throw new Error('You are not authorized to view this offer');
    }

    return offer;
  }

  // Auto-expire old offers
  async expireOldOffers() {
    const result = await ExchangeOffer.expireOldOffers();
    return result;
  }
}

export const exchangeService = new ExchangeService();
