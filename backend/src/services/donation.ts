import mongoose from 'mongoose';
import Transaction from '../models/Transaction';
import ClothingItem from '../models/ClothingItem';
import User from '../models/User';
import CharityPartner from '../models/CharityPartner';
import { tokenService } from './token';
import { achievementService } from './achievement';

export interface DonationData {
  items: string[];
  charityPartnerId: string;
  deliveryMethod: 'pickup' | 'delivery' | 'drop_off';
  deliveryAddress?: {
    county: string;
    town: string;
    specificLocation: string;
    contactPhone: string;
  };
  dropOffLocation?: {
    name: string;
    address: string;
    operatingHours: string;
  };
  message?: string;
  isAnonymous?: boolean;
}

export interface DonationCompletionData {
  receiptNumber?: string;
  itemsReceived: {
    itemId: string;
    condition: string;
    estimatedValue: number;
  }[];
  impactStatement?: string;
  beneficiaryCount?: number;
}

class DonationService {
  // Create a donation transaction
  async createDonation(donorId: string, donationData: DonationData) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate donor exists
      const donor = await User.findById(donorId).session(session);
      if (!donor) {
        throw new Error('Donor not found');
      }

      // Validate charity partner exists and is active
      const charityPartner = await CharityPartner.findById(donationData.charityPartnerId).session(session);
      if (!charityPartner) {
        throw new Error('Charity partner not found');
      }

      if (!charityPartner.isActive || charityPartner.verificationStatus !== 'verified') {
        throw new Error('Charity partner is not active or verified');
      }

      // Validate items exist and belong to donor
      const items = await ClothingItem.find({
        _id: { $in: donationData.items },
        owner: donorId,
        isAvailable: true
      }).session(session);

      if (items.length !== donationData.items.length) {
        throw new Error('Some items are not available or do not belong to you');
      }

      // Check if charity accepts these item types and conditions
      for (const item of items) {
        if (!charityPartner.acceptsItemType(item.category)) {
          throw new Error(`Charity partner does not accept ${item.category} items`);
        }

        if (!charityPartner.requirements.condition.includes(item.condition)) {
          throw new Error(`Charity partner does not accept items in ${item.condition} condition`);
        }
      }

      // Calculate estimated donation value and token rewards
      const estimatedValue = this.calculateDonationValue(items);
      const tokenReward = this.calculateTokenReward(items, charityPartner);

      // Create transaction
      const transaction = new Transaction({
        type: 'donation',
        status: charityPartner.settings.autoAcceptDonations ? 'accepted' : 'pending',
        initiator: donorId,
        recipient: null, // No recipient for donations
        initiatorItems: donationData.items,
        deliveryMethod: donationData.deliveryMethod,
        deliveryAddress: donationData.deliveryAddress,
        charityPartner: {
          id: charityPartner._id,
          name: charityPartner.name,
          category: charityPartner.category
        },
        estimatedValue,
        isAnonymous: donationData.isAnonymous || false,
        timeline: [{
          status: charityPartner.settings.autoAcceptDonations ? 'accepted' : 'pending',
          timestamp: new Date(),
          updatedBy: donorId,
          note: donationData.message || 'Donation initiated'
        }]
      });

      await transaction.save({ session });

      // Update item statuses
      const newStatus = charityPartner.settings.autoAcceptDonations ? 'in_transaction' : 'pending_donation';
      await ClothingItem.updateMany(
        { _id: { $in: donationData.items } },
        { status: newStatus },
        { session }
      );

      // Award initial tokens for donation initiation
      const initiationReward = Math.round(tokenReward * 0.3); // 30% upfront
      await tokenService.addTokens(donorId, initiationReward, 'donation_initiated', {
        transactionId: transaction._id.toString(),
        charityPartner: charityPartner.name,
        itemsCount: items.length,
        estimatedValue
      }, session);

      await session.commitTransaction();
      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Accept a donation (by charity partner)
  async acceptDonation(transactionId: string, charityPartnerId: string) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const transaction = await Transaction.findById(transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.type !== 'donation') {
        throw new Error('Transaction is not a donation');
      }

      if (transaction.status !== 'pending') {
        throw new Error('Donation is not pending');
      }

      if (transaction.charityPartner?.id.toString() !== charityPartnerId) {
        throw new Error('You are not authorized to accept this donation');
      }

      // Update transaction status
      transaction.status = 'accepted';
      transaction.timeline.push({
        status: 'accepted',
        timestamp: new Date(),
        updatedBy: new mongoose.Types.ObjectId(charityPartnerId),
        note: 'Donation accepted by charity partner'
      });

      // Update item statuses
      await ClothingItem.updateMany(
        { _id: { $in: transaction.initiatorItems } },
        { status: 'in_transaction' },
        { session }
      );

      await transaction.save({ session });
      await session.commitTransaction();

      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Complete a donation (when items are received by charity)
  async completeDonation(transactionId: string, charityPartnerId: string, completionData: DonationCompletionData) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const transaction = await Transaction.findById(transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.type !== 'donation') {
        throw new Error('Transaction is not a donation');
      }

      if (transaction.status !== 'accepted') {
        throw new Error('Donation is not in accepted status');
      }

      if (transaction.charityPartner?.id.toString() !== charityPartnerId) {
        throw new Error('You are not authorized to complete this donation');
      }

      // Get charity partner for token calculation
      const charityPartner = await CharityPartner.findById(charityPartnerId).session(session);
      if (!charityPartner) {
        throw new Error('Charity partner not found');
      }

      // Calculate final token reward based on actual received items
      const finalTokenReward = this.calculateFinalTokenReward(completionData.itemsReceived, charityPartner);
      const remainingReward = Math.round(finalTokenReward * 0.7); // 70% on completion

      // Update transaction with completion data
      transaction.status = 'completed';
      transaction.receiptNumber = completionData.receiptNumber;
      transaction.impactStatement = completionData.impactStatement;
      transaction.beneficiaryCount = completionData.beneficiaryCount;
      transaction.timeline.push({
        status: 'completed',
        timestamp: new Date(),
        updatedBy: new mongoose.Types.ObjectId(charityPartnerId),
        note: 'Donation completed - items received by charity'
      });

      // Update items to donated status
      await ClothingItem.updateMany(
        { _id: { $in: transaction.initiatorItems } },
        { 
          status: 'donated',
          donatedAt: new Date(),
          $inc: { 'stats.timesDonated': 1 }
        },
        { session }
      );

      // Award completion tokens to donor
      await tokenService.addTokens(transaction.initiator.toString(), remainingReward, 'donation_completed', {
        transactionId: transaction._id.toString(),
        charityPartner: charityPartner.name,
        receiptNumber: completionData.receiptNumber,
        impactStatement: completionData.impactStatement
      }, session);

      // Update user statistics
      await User.updateOne(
        { _id: transaction.initiator },
        { 
          $inc: { 
            totalDonations: 1, 
            sustainabilityScore: 25,
            totalDonationValue: transaction.estimatedValue || 0
          }
        },
        { session }
      );

      // Update charity partner statistics
      await CharityPartner.updateOne(
        { _id: charityPartnerId },
        {
          $inc: {
            'stats.totalDonationsReceived': 1,
            'stats.totalItemsReceived': completionData.itemsReceived.length,
            'stats.totalBeneficiaries': completionData.beneficiaryCount || 0
          }
        },
        { session }
      );

      // Check for achievements
      await achievementService.checkUserAchievements(transaction.initiator.toString());

      await transaction.save({ session });
      await session.commitTransaction();

      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Cancel a donation
  async cancelDonation(transactionId: string, userId: string, reason: string) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const transaction = await Transaction.findById(transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.type !== 'donation') {
        throw new Error('Transaction is not a donation');
      }

      if (transaction.status === 'completed' || transaction.status === 'cancelled') {
        throw new Error('Donation cannot be cancelled');
      }

      // Check if user is the donor
      if (transaction.initiator.toString() !== userId) {
        throw new Error('You can only cancel your own donations');
      }

      // Update transaction status
      transaction.status = 'cancelled';
      transaction.timeline.push({
        status: 'cancelled',
        timestamp: new Date(),
        updatedBy: new mongoose.Types.ObjectId(userId),
        note: reason
      });

      // Release items back to available status
      await ClothingItem.updateMany(
        { _id: { $in: transaction.initiatorItems } },
        { isAvailable: true },
        { session }
      );

      await transaction.save({ session });
      await session.commitTransaction();

      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Get donation recommendations for user
  async getDonationRecommendations(userId: string, category?: string) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Get user's available items
      const userItems = await ClothingItem.find({
        owner: userId,
        isAvailable: true,
        ...(category && { category })
      });

      if (userItems.length === 0) {
        return [];
      }

      // Find suitable charity partners
      const recommendations = await CharityPartner.aggregate([
        {
          $match: {
            isActive: true,
            verificationStatus: 'verified',
            acceptedItemTypes: { $in: userItems.map(item => item.category) },
            'contactInfo.address.county': user.location.county // Same county preference
          }
        },
        {
          $addFields: {
            matchScore: {
              $size: {
                $setIntersection: [
                  '$acceptedItemTypes',
                  userItems.map(item => item.category)
                ]
              }
            },
            estimatedReward: {
              $multiply: [
                '$tokenRewards.baseReward',
                userItems.length
              ]
            }
          }
        },
        {
          $sort: { matchScore: -1, 'stats.averageRating': -1 }
        },
        {
          $limit: 10
        }
      ]);

      return recommendations;

    } catch (error) {
      throw error;
    }
  }

  // Get donation impact report for user
  async getDonationImpact(userId: string) {
    try {
      const impactData = await Transaction.aggregate([
        {
          $match: {
            initiator: new mongoose.Types.ObjectId(userId),
            type: 'donation',
            status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            totalDonations: { $sum: 1 },
            totalItems: { $sum: { $size: '$initiatorItems' } },
            totalValue: { $sum: '$estimatedValue' },
            totalBeneficiaries: { $sum: '$beneficiaryCount' },
            charityPartners: { $addToSet: '$charityPartner.name' }
          }
        }
      ]);

      const impact = impactData[0] || {
        totalDonations: 0,
        totalItems: 0,
        totalValue: 0,
        totalBeneficiaries: 0,
        charityPartners: []
      };

      // Calculate environmental impact estimates
      impact.environmentalImpact = {
        co2Saved: Math.round(impact.totalItems * 2.3), // kg CO2 per item
        waterSaved: Math.round(impact.totalItems * 2700), // liters per item
        wasteReduced: Math.round(impact.totalItems * 0.5) // kg waste per item
      };

      return impact;

    } catch (error) {
      throw error;
    }
  }

  // Private method to calculate donation value
  private calculateDonationValue(items: any[]): number {
    return items.reduce((total, item) => {
      let baseValue = 50; // Base donation value
      
      // Condition multiplier
      const conditionMultipliers = {
        'excellent': 1.5,
        'very_good': 1.3,
        'good': 1.0,
        'fair': 0.8,
        'poor': 0.6
      };
      
      const conditionMultiplier = conditionMultipliers[item.condition as keyof typeof conditionMultipliers] || 1.0;
      
      return total + (baseValue * conditionMultiplier);
    }, 0);
  }

  // Private method to calculate token reward
  private calculateTokenReward(items: any[], charityPartner: any): number {
    let totalReward = 0;

    for (const item of items) {
      const baseReward = charityPartner.tokenRewards.baseReward;
      const qualityMultiplier = charityPartner.tokenRewards.qualityMultiplier[item.condition] || 1.0;
      totalReward += Math.round(baseReward * qualityMultiplier);
    }

    return totalReward;
  }

  // Private method to calculate final token reward based on received items
  private calculateFinalTokenReward(receivedItems: any[], charityPartner: any): number {
    let totalReward = 0;

    for (const item of receivedItems) {
      const baseReward = charityPartner.tokenRewards.baseReward;
      const qualityMultiplier = charityPartner.tokenRewards.qualityMultiplier[item.condition] || 1.0;
      totalReward += Math.round(baseReward * qualityMultiplier);
    }

    return totalReward;
  }
}

export const donationService = new DonationService();

// Export types for use in routes
export type { DonationData, DonationCompletionData };
