import mongoose from 'mongoose';
import Transaction from '../models/Transaction';
import ClothingItem from '../models/ClothingItem';
import User from '../models/User';
import { tokenService } from './token';
import { achievementService } from './achievement';

export interface SwapData {
  initiatorItems: string[];
  recipientItems: string[];
  deliveryMethod: 'pickup' | 'delivery' | 'meetup';
  deliveryAddress?: {
    county: string;
    town: string;
    specificLocation: string;
    contactPhone: string;
  };
  meetupLocation?: {
    name: string;
    address: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
    scheduledTime: Date;
  };
  notes?: string;
}

class SwapService {
  // Create a direct swap transaction (when both parties agree)
  async createSwapTransaction(initiatorId: string, recipientId: string, swapData: SwapData) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate users exist
      const [initiator, recipient] = await Promise.all([
        User.findById(initiatorId).session(session),
        User.findById(recipientId).session(session)
      ]);

      if (!initiator || !recipient) {
        throw new Error('User not found');
      }

      // Validate initiator items
      const initiatorItems = await ClothingItem.find({
        _id: { $in: swapData.initiatorItems },
        owner: initiatorId,
        status: 'available'
      }).session(session);

      if (initiatorItems.length !== swapData.initiatorItems.length) {
        throw new Error('Some initiator items are not available or do not belong to you');
      }

      // Validate recipient items
      const recipientItems = await ClothingItem.find({
        _id: { $in: swapData.recipientItems },
        owner: recipientId,
        status: 'available'
      }).session(session);

      if (recipientItems.length !== swapData.recipientItems.length) {
        throw new Error('Some recipient items are not available or do not belong to the recipient');
      }

      // Calculate swap value and quality scores
      const initiatorValue = this.calculateItemsValue(initiatorItems);
      const recipientValue = this.calculateItemsValue(recipientItems);
      const initiatorQuality = this.calculateAverageQuality(initiatorItems);
      const recipientQuality = this.calculateAverageQuality(recipientItems);

      // Create transaction
      const transaction = new Transaction({
        type: 'swap',
        status: 'accepted', // Direct swap starts as accepted
        initiator: initiatorId,
        recipient: recipientId,
        initiatorItems: swapData.initiatorItems,
        recipientItems: swapData.recipientItems,
        deliveryMethod: swapData.deliveryMethod,
        deliveryAddress: swapData.deliveryAddress,
        meetupLocation: swapData.meetupLocation,
        timeline: [{
          status: 'accepted',
          timestamp: new Date(),
          updatedBy: initiatorId,
          note: swapData.notes || 'Direct swap initiated'
        }]
      });

      await transaction.save({ session });

      // Update item statuses
      await ClothingItem.updateMany(
        { _id: { $in: [...swapData.initiatorItems, ...swapData.recipientItems] } },
        { status: 'in_transaction' },
        { session }
      );

      // Award tokens for successful swap initiation
      const baseReward = 20;
      const qualityBonus = Math.round((initiatorQuality + recipientQuality) / 2 * 5);
      const totalReward = baseReward + qualityBonus;

      await Promise.all([
        tokenService.addTokens(initiatorId, totalReward, 'swap_initiated', {
          transactionId: transaction._id.toString(),
          itemsCount: initiatorItems.length,
          qualityScore: initiatorQuality
        }, session),
        tokenService.addTokens(recipientId, totalReward, 'swap_participated', {
          transactionId: transaction._id.toString(),
          itemsCount: recipientItems.length,
          qualityScore: recipientQuality
        }, session)
      ]);

      await session.commitTransaction();
      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Complete a swap transaction
  async completeSwap(transactionId: string, userId: string, completionData: {
    rating?: number;
    comment?: string;
    qualityConfirmed: boolean;
  }) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const transaction = await Transaction.findById(transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.type !== 'swap') {
        throw new Error('Transaction is not a swap');
      }

      if (transaction.status !== 'accepted') {
        throw new Error('Transaction is not in accepted status');
      }

      // Check if user is part of this transaction
      const isInitiator = transaction.initiator.toString() === userId;
      const isRecipient = transaction.recipient?.toString() === userId;

      if (!isInitiator && !isRecipient) {
        throw new Error('You are not part of this transaction');
      }

      // Add rating if provided
      if (completionData.rating) {
        if (isInitiator) {
          transaction.ratings = transaction.ratings || {};
          transaction.ratings.initiatorRating = {
            score: completionData.rating,
            comment: completionData.comment,
            ratedAt: new Date()
          };
        } else {
          transaction.ratings = transaction.ratings || {};
          transaction.ratings.recipientRating = {
            score: completionData.rating,
            comment: completionData.comment,
            ratedAt: new Date()
          };
        }
      }

      // Check if both parties have confirmed completion
      const bothRated = transaction.ratings?.initiatorRating && transaction.ratings?.recipientRating;
      
      if (bothRated || completionData.qualityConfirmed) {
        // Complete the transaction
        transaction.status = 'completed';
        transaction.timeline.push({
          status: 'completed',
          timestamp: new Date(),
          updatedBy: new mongoose.Types.ObjectId(userId),
          note: 'Swap completed successfully'
        });

        // Transfer item ownership
        await ClothingItem.updateMany(
          { _id: { $in: transaction.initiatorItems } },
          {
            owner: transaction.recipient,
            isAvailable: true,
            $inc: { 'stats.timesSwapped': 1 }
          },
          { session }
        );

        await ClothingItem.updateMany(
          { _id: { $in: transaction.recipientItems || [] } },
          {
            owner: transaction.initiator,
            isAvailable: true,
            $inc: { 'stats.timesSwapped': 1 }
          },
          { session }
        );

        // Update user statistics
        await User.updateOne(
          { _id: transaction.initiator },
          { $inc: { totalSwaps: 1, sustainabilityScore: 15 } },
          { session }
        );

        await User.updateOne(
          { _id: transaction.recipient },
          { $inc: { totalSwaps: 1, sustainabilityScore: 15 } },
          { session }
        );

        // Award completion bonus tokens
        const completionBonus = 30;
        const qualityBonus = bothRated ? 
          Math.round(((transaction.ratings.initiatorRating?.score || 0) + 
                     (transaction.ratings.recipientRating?.score || 0)) / 2 * 10) : 0;

        await Promise.all([
          tokenService.addTokens(transaction.initiator.toString(), completionBonus + qualityBonus, 'swap_completed', {
            transactionId: transaction._id.toString(),
            rating: transaction.ratings?.recipientRating?.score,
            qualityBonus
          }, session),
          tokenService.addTokens(transaction.recipient!.toString(), completionBonus + qualityBonus, 'swap_completed', {
            transactionId: transaction._id.toString(),
            rating: transaction.ratings?.initiatorRating?.score,
            qualityBonus
          }, session)
        ]);

        // Send completion notifications
        try {
          const { notificationIntegrationService } = await import('./notificationIntegration');
          const initiatorUser = await User.findById(transaction.initiator).session(session);
          const recipientUser = await User.findById(transaction.recipient).session(session);

          if (initiatorUser && recipientUser) {
            // Get item names for better notification context
            const initiatorItemNames = transaction.initiatorItems ?
              (await ClothingItem.find({ _id: { $in: transaction.initiatorItems } }).session(session))
                .map(item => item.name).join(', ') : 'items';

            // Notify both parties about completion
            await Promise.all([
              notificationIntegrationService.sendExchangeStatusUpdate(
                transaction.initiator.toString(),
                'completed',
                initiatorItemNames,
                recipientUser.fullName,
                transaction._id.toString()
              ),
              notificationIntegrationService.sendExchangeStatusUpdate(
                transaction.recipient!.toString(),
                'completed',
                initiatorItemNames,
                initiatorUser.fullName,
                transaction._id.toString()
              )
            ]);
          }
        } catch (notificationError) {
          // Don't fail the transaction for notification errors
          console.error('Failed to send swap completion notifications:', notificationError);
        }

        // Check for achievements
        await Promise.all([
          achievementService.checkUserAchievements(transaction.initiator.toString()),
          achievementService.checkUserAchievements(transaction.recipient!.toString())
        ]);
      }

      await transaction.save({ session });
      await session.commitTransaction();

      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Cancel a swap transaction
  async cancelSwap(transactionId: string, userId: string, reason: string) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const transaction = await Transaction.findById(transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.type !== 'swap') {
        throw new Error('Transaction is not a swap');
      }

      if (transaction.status === 'completed' || transaction.status === 'cancelled') {
        throw new Error('Transaction cannot be cancelled');
      }

      // Check if user is part of this transaction
      const isInitiator = transaction.initiator.toString() === userId;
      const isRecipient = transaction.recipient?.toString() === userId;

      if (!isInitiator && !isRecipient) {
        throw new Error('You are not part of this transaction');
      }

      // Update transaction status
      transaction.status = 'cancelled';
      transaction.timeline.push({
        status: 'cancelled',
        timestamp: new Date(),
        updatedBy: new mongoose.Types.ObjectId(userId),
        note: reason
      });

      // Release items back to available status
      await ClothingItem.updateMany(
        { _id: { $in: [...transaction.initiatorItems, ...(transaction.recipientItems || [])] } },
        { status: 'available' },
        { session }
      );

      await transaction.save({ session });
      await session.commitTransaction();

      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Get swap recommendations for a user
  async getSwapRecommendations(userId: string, itemId?: string) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      let userItems;
      if (itemId) {
        // Get recommendations for a specific item
        userItems = await ClothingItem.find({ _id: itemId, owner: userId, status: 'available' });
      } else {
        // Get user's available items
        userItems = await ClothingItem.find({ owner: userId, status: 'available' }).limit(5);
      }

      if (userItems.length === 0) {
        return [];
      }

      // Find potential swap partners
      const recommendations = await ClothingItem.aggregate([
        {
          $match: {
            owner: { $ne: new mongoose.Types.ObjectId(userId) },
            status: 'available',
            // Match similar categories or sizes
            $or: [
              { category: { $in: userItems.map(item => item.category) } },
              { size: { $in: userItems.map(item => item.size) } }
            ]
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'owner',
            foreignField: '_id',
            as: 'ownerInfo'
          }
        },
        {
          $unwind: '$ownerInfo'
        },
        {
          $match: {
            'ownerInfo.location.county': user.location.county, // Same county for easier exchange
            'ownerInfo.rating': { $gte: 3.0 } // Good rating users only
          }
        },
        {
          $group: {
            _id: '$owner',
            items: { $push: '$$ROOT' },
            ownerInfo: { $first: '$ownerInfo' },
            totalItems: { $sum: 1 }
          }
        },
        {
          $match: {
            totalItems: { $gte: 1 }
          }
        },
        {
          $sort: { 'ownerInfo.rating': -1, totalItems: -1 }
        },
        {
          $limit: 10
        }
      ]);

      return recommendations;

    } catch (error) {
      throw error;
    }
  }

  // Calculate total value of items based on condition and category
  private calculateItemsValue(items: any[]): number {
    return items.reduce((total, item) => {
      let baseValue = 100; // Base value
      
      // Condition multiplier
      const conditionMultipliers = {
        'excellent': 1.5,
        'very_good': 1.3,
        'good': 1.0,
        'fair': 0.7,
        'poor': 0.5
      };
      
      const conditionMultiplier = conditionMultipliers[item.condition as keyof typeof conditionMultipliers] || 1.0;
      
      // Category multiplier (some categories are more valuable)
      const categoryMultipliers = {
        'Formal': 1.3,
        'Outerwear': 1.2,
        'Shoes': 1.1,
        'Accessories': 0.9
      };
      
      const categoryMultiplier = categoryMultipliers[item.category as keyof typeof categoryMultipliers] || 1.0;
      
      return total + (baseValue * conditionMultiplier * categoryMultiplier);
    }, 0);
  }

  // Calculate average quality score of items
  private calculateAverageQuality(items: any[]): number {
    const qualityScores = {
      'excellent': 5,
      'very_good': 4,
      'good': 3,
      'fair': 2,
      'poor': 1
    };

    const totalScore = items.reduce((sum, item) => {
      return sum + (qualityScores[item.condition as keyof typeof qualityScores] || 3);
    }, 0);

    return totalScore / items.length;
  }
}

export const swapService = new SwapService();
